package smtp_credential_test

import (
	"net/http"

	"git.metadiv.io/metadiv-studio/metadev"
	"git.metadiv.io/metadiv-studio/metaorm"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"git.metadiv.io/metadiv-studio-modules/smtp_credential"
)

var _ = Describe("ApiSmtpCredentialUpdate Handler", func() {
	var (
		mockDB     smtp_credential.MockDatabaseExecutor
		db         metaorm.Database
		engine     metadev.Engine
		apiHandler *metadev.ApiHandlerImpl[smtp_credential.UpdateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO]
	)

	BeforeEach(func() {
		engine = metadev.NewTestEngine()
		apiHandler = smtp_credential.ApiSmtpCredentialUpdate
	})

	AfterEach(func() {
		if mockDB != nil {
			mockDB.Close()
		}
	})

	setupDatabase := func() {
		mockDB = smtp_credential.NewMockDatabaseExecutor()
		db = mockDB.New()
	}

	createTestCredential := func(repo smtp_credential.SMTPCredentialRepository, host, username string, port int, active bool) *smtp_credential.SMTPCredential {
		credential := &smtp_credential.SMTPCredential{}
		credential.Host.Set(host)
		credential.Port.Set(port)
		credential.Username.Set(username)
		credential.Password.Set("testpassword")
		credential.Active.Set(active)
		savedCredential, err := repo.Save(credential).Model()
		Expect(err).To(BeNil())
		return savedCredential
	}

	Context("when updating an existing SMTP credential", func() {
		It("should update credential successfully", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)
			testCredential := createTestCredential(credentialRepo, "smtp.original.com", "<EMAIL>", 587, true)

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.UpdateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO](
				1, 1, 1, false, engine, apiHandler,
				&smtp_credential.UpdateSmtpCredentialRequest{
					RequestPathId: metadev.RequestPathId{ID: testCredential.Model.ID},
					CreateSmtpCredentialRequest: smtp_credential.CreateSmtpCredentialRequest{
						Host: "smtp.updated.com", Port: 465, Username: "<EMAIL>", Password: "newpassword",
					},
					Active: false,
				},
			)
			Expect(err).To(BeNil())

			apiHandler.Handler(ctx, db, logger, &smtp_credential.UpdateSmtpCredentialRequest{
				RequestPathId: metadev.RequestPathId{ID: testCredential.Model.ID},
				CreateSmtpCredentialRequest: smtp_credential.CreateSmtpCredentialRequest{
					Host: "smtp.updated.com", Port: 465, Username: "<EMAIL>", Password: "newpassword",
				},
				Active: false,
			})

			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody.Host).To(Equal("smtp.updated.com"))
			Expect(responseBody.Port).To(Equal(465))
			Expect(responseBody.Active).To(BeFalse())
		})
	})

	Context("when credential does not exist", func() {
		It("should return credential not found error", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.UpdateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO](
				1, 1, 1, false, engine, apiHandler,
				&smtp_credential.UpdateSmtpCredentialRequest{
					RequestPathId: metadev.RequestPathId{ID: 999},
					CreateSmtpCredentialRequest: smtp_credential.CreateSmtpCredentialRequest{
						Host: "smtp.nonexistent.com", Port: 587, Username: "<EMAIL>", Password: "password",
					},
					Active: true,
				},
			)
			Expect(err).To(BeNil())

			apiHandler.Handler(ctx, db, logger, &smtp_credential.UpdateSmtpCredentialRequest{
				RequestPathId: metadev.RequestPathId{ID: 999},
				CreateSmtpCredentialRequest: smtp_credential.CreateSmtpCredentialRequest{
					Host: "smtp.nonexistent.com", Port: 587, Username: "<EMAIL>", Password: "password",
				},
				Active: true,
			})

			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusBadRequest))
			Expect(ctx.GetResponse().Success).To(BeFalse())
			Expect(ctx.GetResponse().Error).To(Equal("credential not found"))
		})
	})
})
