package smtp_credential

import (
	"git.metadiv.io/metadiv-studio/metadev"
	"git.metadiv.io/metadiv-studio/metaorm"
)

type ListSmtpCredentialRequest struct {
	metadev.RequestListing
	ActiveOnly bool `form:"active_only"`
}

var ApiSmtpCredentialList = metadev.Get[ListSmtpCredentialRequest, []SMTPCredentialDTO](Module).
	Name("listSmtpCredential").
	Route("/smtp/credential").
	Description(`
List all SMTP credentials.
`).
	Handler(func(ctx metadev.ApiContext[ListSmtpCredentialRequest, []SMTPCredentialDTO], db metaorm.Database, logger metadev.Logger, req *ListSmtpCredentialRequest) {
		credentialRepo := NewSMTPCredentialRepository(db)
		results, pagination, err := credentialRepo.FilteredPagedFindMany(
			&req.PaginationQueryImpl,
			&req.SortingQueryImpl,
			req.Keyword,
			req.ActiveOnly,
		)
		if err != nil {
			logger.Error(err.<PERSON>rror())
			ctx.Error(err)
			return
		}
		ds := metaorm.ConvertDTOs[SMTPCredential, SMTPCredentialDTO](results)
		ctx.OK(&ds, pagination)
	})
