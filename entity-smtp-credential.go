package smtp_credential

import "git.metadiv.io/metadiv-studio/metaorm"

type SMTPCredential struct {
	metaorm.Model
	UUID     metaorm.FieldUUID
	Host     metaorm.FieldVarchar
	Port     metaorm.FieldInt
	Username metaorm.FieldVarchar
	Password metaorm.FieldVarchar
	Active   metaorm.FieldBool
}

func (SMTPCredential) TableName() string {
	return "smtp_credentials"
}

type SMTPCredentialDTO struct {
	metaorm.ModelDTO
	UUID     string `json:"uuid"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	Active   bool   `json:"active"`
}

func (e *SMTPCredential) DTO() *SMTPCredentialDTO {
	return &SMTPCredentialDTO{
		ModelDTO: e.Model.DTO(),
		UUID:     e.UUID.Get(),
		Host:     e.Host.Get(),
		Port:     e.Port.Get(),
		Username: e.Username.Get(),
		Password: e.Password.Get(),
		Active:   e.Active.Get(),
	}
}
