package smtp_credential_test

import (
	"net/http"

	"git.metadiv.io/metadiv-studio/metadev"
	"git.metadiv.io/metadiv-studio/metaorm"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"git.metadiv.io/metadiv-studio-modules/smtp_credential"
)

var _ = Describe("ApiSmtpCredentialDelete Handler", func() {
	var (
		mockDB     smtp_credential.MockDatabaseExecutor
		db         metaorm.Database
		engine     metadev.Engine
		apiHandler *metadev.ApiHandlerImpl[metadev.RequestPathId, metadev.Empty]
	)

	BeforeEach(func() {
		engine = metadev.NewTestEngine()
		apiHandler = smtp_credential.ApiSmtpCredentialDelete
	})

	AfterEach(func() {
		if mockDB != nil {
			mockDB.Close()
		}
	})

	setupDatabase := func() {
		mockDB = smtp_credential.NewMockDatabaseExecutor()
		db = mockDB.New()
	}

	createTestCredential := func(repo smtp_credential.SMTPCredentialRepository, host, username string, port int, active bool) *smtp_credential.SMTPCredential {
		credential := &smtp_credential.SMTPCredential{}
		credential.Host.Set(host)
		credential.Port.Set(port)
		credential.Username.Set(username)
		credential.Password.Set("testpassword")
		credential.Active.Set(active)
		savedCredential, err := repo.Save(credential).Model()
		Expect(err).To(BeNil())
		return savedCredential
	}

	Context("when deleting an existing SMTP credential", func() {
		It("should delete credential successfully", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)
			testCredential := createTestCredential(credentialRepo, "smtp.delete.com", "<EMAIL>", 587, true)

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[metadev.RequestPathId, metadev.Empty](
				1, 1, 1, false, engine, apiHandler,
				&metadev.RequestPathId{ID: testCredential.Model.ID},
			)
			Expect(err).To(BeNil())

			apiHandler.Handler(ctx, db, logger, &metadev.RequestPathId{ID: testCredential.Model.ID})

			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())

			// Verify credential is actually deleted
			_, err = credentialRepo.FindById(testCredential.Model.ID)
			Expect(err).ToNot(BeNil()) // Should return error for deleted credential
		})
	})

	Context("when credential does not exist", func() {
		It("should return credential not found error", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[metadev.RequestPathId, metadev.Empty](
				1, 1, 1, false, engine, apiHandler,
				&metadev.RequestPathId{ID: 999},
			)
			Expect(err).To(BeNil())

			apiHandler.Handler(ctx, db, logger, &metadev.RequestPathId{ID: 999})

			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusBadRequest))
			Expect(ctx.GetResponse().Success).To(BeFalse())
			Expect(ctx.GetResponse().Error).To(Equal("credential not found"))
		})
	})
})
