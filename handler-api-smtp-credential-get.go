package smtp_credential

import (
	"git.metadiv.io/metadiv-studio/metadev"
	"git.metadiv.io/metadiv-studio/metaorm"
)

var ApiSmtpCredentialGet = metadev.Get[metadev.RequestPathId, SMTPCredentialDTO](Module).
	Name("getSmtpCredential").
	Route("/smtp/credential/:id").
	Description(`
Get an existing SMTP credential.
`).
	Handler(func(ctx metadev.ApiContext[metadev.RequestPathId, SMTPCredentialDTO], db metaorm.Database, logger metadev.Logger, req *metadev.RequestPathId) {
		credentialRepo := NewSMTPCredentialRepository(db)
		e, err := credentialRepo.FindById(req.ID)
		if err != nil {
			logger.Error(err.Error())
			ctx.Error(err)
			return
		}
		if e == nil {
			ctx.Error(ErrCredentialNotFound)
			return
		}
		ctx.OK(e.DTO())
	})
