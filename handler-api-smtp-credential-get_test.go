package smtp_credential_test

import (
	"net/http"

	"git.metadiv.io/metadiv-studio/metadev"
	"git.metadiv.io/metadiv-studio/metaorm"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"git.metadiv.io/metadiv-studio-modules/smtp_credential"
)

var _ = Describe("ApiSmtpCredentialGet Handler", func() {
	var (
		mockDB     smtp_credential.MockDatabaseExecutor
		db         metaorm.Database
		engine     metadev.Engine
		apiHandler *metadev.ApiHandlerImpl[metadev.RequestPathId, smtp_credential.SMTPCredentialDTO]
	)

	BeforeEach(func() {
		// Initialize test engine
		engine = metadev.NewTestEngine()

		// Get the API handler from the module
		apiHandler = smtp_credential.ApiSmtpCredentialGet
	})

	AfterEach(func() {
		if mockDB != nil {
			mockDB.Close()
		}
	})

	setupDatabase := func() {
		mockDB = smtp_credential.NewMockDatabaseExecutor()
		db = mockDB.New()
	}

	createTestCredential := func(repo smtp_credential.SMTPCredentialRepository, host, username string, port int, active bool) *smtp_credential.SMTPCredential {
		credential := &smtp_credential.SMTPCredential{}
		credential.Host.Set(host)
		credential.Port.Set(port)
		credential.Username.Set(username)
		credential.Password.Set("testpassword")
		credential.Active.Set(active)
		savedCredential, err := repo.Save(credential).Model()
		Expect(err).To(BeNil())
		Expect(savedCredential).ToNot(BeNil())
		return savedCredential
	}

	Context("when getting an existing SMTP credential", func() {
		It("should return the credential successfully", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)

			// Create a test credential
			testCredential := createTestCredential(credentialRepo, "smtp.gmail.com", "<EMAIL>", 587, true)

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[metadev.RequestPathId, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&metadev.RequestPathId{ID: testCredential.Model.ID},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &metadev.RequestPathId{ID: testCredential.Model.ID})

			// Verify response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(responseBody.Host).To(Equal("smtp.gmail.com"))
			Expect(responseBody.Port).To(Equal(587))
			Expect(responseBody.Username).To(Equal("<EMAIL>"))
			Expect(responseBody.Active).To(BeTrue())
		})

		It("should return inactive credential", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)

			// Create an inactive credential
			inactiveCredential := createTestCredential(credentialRepo, "smtp.inactive.com", "<EMAIL>", 465, false)

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[metadev.RequestPathId, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&metadev.RequestPathId{ID: inactiveCredential.Model.ID},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &metadev.RequestPathId{ID: inactiveCredential.Model.ID})

			// Verify response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(responseBody.Host).To(Equal("smtp.inactive.com"))
			Expect(responseBody.Port).To(Equal(465))
			Expect(responseBody.Username).To(Equal("<EMAIL>"))
			Expect(responseBody.Active).To(BeFalse())
		})

		It("should return credential with different port", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)

			// Create a credential with custom port
			customPortCredential := createTestCredential(credentialRepo, "mail.custom.com", "<EMAIL>", 2525, true)

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[metadev.RequestPathId, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&metadev.RequestPathId{ID: customPortCredential.Model.ID},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &metadev.RequestPathId{ID: customPortCredential.Model.ID})

			// Verify response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(responseBody.Host).To(Equal("mail.custom.com"))
			Expect(responseBody.Port).To(Equal(2525))
			Expect(responseBody.Username).To(Equal("<EMAIL>"))
		})
	})

	Context("when credential does not exist", func() {
		It("should return credential not found error", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[metadev.RequestPathId, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&metadev.RequestPathId{ID: 999},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &metadev.RequestPathId{ID: 999})

			// Verify error response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusBadRequest))
			Expect(ctx.GetResponse().Success).To(BeFalse())
			Expect(ctx.GetResponse().Error).To(Equal("credential not found"))
		})

		It("should return credential not found error for ID 0", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[metadev.RequestPathId, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&metadev.RequestPathId{ID: 0},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &metadev.RequestPathId{ID: 0})

			// Verify error response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusBadRequest))
			Expect(ctx.GetResponse().Success).To(BeFalse())
			Expect(ctx.GetResponse().Error).To(Equal("credential not found"))
		})
	})

	Context("when testing edge cases", func() {
		It("should handle credential with special characters", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)

			// Create a credential with special characters
			specialCredential := &smtp_credential.SMTPCredential{}
			specialCredential.Host.Set("smtp.example-domain.com")
			specialCredential.Port.Set(587)
			specialCredential.Username.Set("<EMAIL>")
			specialCredential.Password.Set("p@ssw0rd!")
			specialCredential.Active.Set(true)
			savedCredential, err := credentialRepo.Save(specialCredential).Model()
			Expect(err).To(BeNil())
			Expect(savedCredential).ToNot(BeNil())

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[metadev.RequestPathId, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&metadev.RequestPathId{ID: savedCredential.Model.ID},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &metadev.RequestPathId{ID: savedCredential.Model.ID})

			// Verify response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(responseBody.Host).To(Equal("smtp.example-domain.com"))
			Expect(responseBody.Username).To(Equal("<EMAIL>"))
			Expect(responseBody.Password).To(Equal("p@ssw0rd!"))
		})

		It("should handle credential with very long host name", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)

			longHost := "very-long-subdomain-name-that-exceeds-normal-length-limits.example.com"
			longHostCredential := &smtp_credential.SMTPCredential{}
			longHostCredential.Host.Set(longHost)
			longHostCredential.Port.Set(587)
			longHostCredential.Username.Set("<EMAIL>")
			longHostCredential.Password.Set("testpassword")
			longHostCredential.Active.Set(true)
			savedCredential, err := credentialRepo.Save(longHostCredential).Model()
			Expect(err).To(BeNil())
			Expect(savedCredential).ToNot(BeNil())

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[metadev.RequestPathId, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&metadev.RequestPathId{ID: savedCredential.Model.ID},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &metadev.RequestPathId{ID: savedCredential.Model.ID})

			// Verify response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(responseBody.Host).To(Equal(longHost))
		})

		It("should handle credential with different port ranges", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)

			testPorts := []int{25, 465, 587, 2525, 1025, 65535}

			for _, port := range testPorts {
				portCredential := createTestCredential(credentialRepo, "smtp.example.com", "<EMAIL>", port, true)

				ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[metadev.RequestPathId, smtp_credential.SMTPCredentialDTO](
					1,     // userId
					1,     // workspaceId
					1,     // workspaceUserId
					false, // isAdmin
					engine,
					apiHandler,
					&metadev.RequestPathId{ID: portCredential.Model.ID},
				)
				Expect(err).To(BeNil())

				// Execute the handler
				apiHandler.Handler(ctx, db, logger, &metadev.RequestPathId{ID: portCredential.Model.ID})

				// Verify response
				Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
				Expect(ctx.GetResponse().Success).To(BeTrue())
				responseBody := ctx.GetResponse().Data
				Expect(responseBody).ToNot(BeNil())
				Expect(responseBody.Port).To(Equal(port))
			}
		})
	})

	Context("when testing data consistency", func() {
		It("should not expose password in DTO", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)

			// Create a test credential
			testCredential := createTestCredential(credentialRepo, "smtp.secure.com", "<EMAIL>", 587, true)

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[metadev.RequestPathId, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&metadev.RequestPathId{ID: testCredential.Model.ID},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &metadev.RequestPathId{ID: testCredential.Model.ID})

			// Verify response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())

			// Verify DTO structure doesn't have password field
			// This is implicitly tested by the DTO struct definition
			Expect(responseBody.Host).ToNot(BeEmpty())
			Expect(responseBody.Username).ToNot(BeEmpty())
		})

		It("should return correct model metadata", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)

			// Create a test credential
			testCredential := createTestCredential(credentialRepo, "smtp.meta.com", "<EMAIL>", 587, true)

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[metadev.RequestPathId, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&metadev.RequestPathId{ID: testCredential.Model.ID},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &metadev.RequestPathId{ID: testCredential.Model.ID})

			// Verify response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())

			// Verify model metadata is present
			Expect(responseBody.ModelDTO.ID).To(Equal(testCredential.Model.ID))
			Expect(responseBody.ModelDTO.CreatedAt).ToNot(BeNil())
			Expect(responseBody.ModelDTO.UpdatedAt).ToNot(BeNil())
		})
	})
})
