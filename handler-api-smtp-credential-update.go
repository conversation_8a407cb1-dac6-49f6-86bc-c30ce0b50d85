package smtp_credential

import (
	"git.metadiv.io/metadiv-studio/metadev"
	"git.metadiv.io/metadiv-studio/metaorm"
)

type UpdateSmtpCredentialRequest struct {
	metadev.RequestPathId
	CreateSmtpCredentialRequest
	Active bool `json:"active"`
}

func (r *UpdateSmtpCredentialRequest) ToEntity(e *SMTPCredential) *SMTPCredential {
	e.Host.Set(r.Host)
	e.Port.Set(r.Port)
	e.Username.Set(r.Username)
	e.Password.Set(r.Password)
	e.Active.Set(r.Active)
	return e
}

var ApiSmtpCredentialUpdate = metadev.Get[UpdateSmtpCredentialRequest, SMTPCredentialDTO](Module).
	Name("updateSmtpCredential").
	Route("/smtp/credential/:id").
	Description(`
Update an existing SMTP credential.
`).
	Handler(func(ctx metadev.ApiContext[UpdateSmtpCredentialRequest, SMTPCredentialDTO], db metaorm.Database, logger metadev.Logger, req *UpdateSmtpCredentialRequest) {

		credentialRepo := NewSMTPCredentialRepository(db)
		e, err := credentialRepo.FindById(req.ID)
		if err != nil {
			logger.Error(err.Error())
			ctx.Error(err)
			return
		}
		if e == nil {
			ctx.Error(ErrCredentialNotFound)
			return
		}

		e = req.ToEntity(e)
		e, err = credentialRepo.Save(e).Model()
		if err != nil {
			logger.Error(err.Error())
			ctx.Error(err)
			return
		}
		ctx.OK(e.DTO())
	})
