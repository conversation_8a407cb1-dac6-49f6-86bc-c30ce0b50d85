package smtp_credential

import (
	"os"

	"git.metadiv.io/metadiv-studio/metaorm"
	"git.metadiv.io/metadiv-studio/nanoid"
)

func NewMockDatabaseExecutor() MockDatabaseExecutor {
	return &MockDatabaseExecutorImpl{}
}

type MockDatabaseExecutor interface {
	New() metaorm.Database
	Close()
}

type MockDatabaseExecutorImpl struct {
	db     metaorm.Database
	dbPath string
}

func (e *MockDatabaseExecutorImpl) New() metaorm.Database {
	e.dbPath = nanoid.NewSafeGenerator().Generate() + ".db"
	db, err := metaorm.NewConnector().Sqlite().Path(e.dbPath).Connect()
	if err != nil {
		panic(err)
	}

	db.AutoMigrate(&SMTPCredential{})

	smtpCredentialRepo := NewSMTPCredentialRepository(db)

	// Create a sample SMTP credential for testing
	sampleCredential := &SMTPCredential{}
	sampleCredential.Host.Set("smtp.example.com")
	sampleCredential.Port.Set(587)
	sampleCredential.Username.Set("<EMAIL>")
	sampleCredential.Password.Set("testpassword")
	sampleCredential.Active.Set(true)
	smtpCredentialRepo.Save(sampleCredential)

	// Create another sample SMTP credential
	anotherCredential := &SMTPCredential{}
	anotherCredential.Host.Set("smtp.gmail.com")
	anotherCredential.Port.Set(465)
	anotherCredential.Username.Set("<EMAIL>")
	anotherCredential.Password.Set("userpassword")
	anotherCredential.Active.Set(false)
	smtpCredentialRepo.Save(anotherCredential)

	e.db = db
	return db
}

func (e *MockDatabaseExecutorImpl) Close() {
	os.Remove(e.dbPath)
}
