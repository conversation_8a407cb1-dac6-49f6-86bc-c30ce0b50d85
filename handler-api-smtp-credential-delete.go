package smtp_credential

import (
	"git.metadiv.io/metadiv-studio/metadev"
	"git.metadiv.io/metadiv-studio/metaorm"
)

var ApiSmtpCredentialDelete = metadev.Get[metadev.RequestPathId, metadev.Empty](Module).
	Name("deleteSmtpCredential").
	Route("/smtp/credential/:id").
	Description(`
Delete an existing SMTP credential.
`).
	Handler(func(ctx metadev.ApiContext[metadev.RequestPathId, metadev.Empty], db metaorm.Database, logger metadev.Logger, req *metadev.RequestPathId) {
		credentialRepo := NewSMTPCredentialRepository(db)
		e, err := credentialRepo.FindById(req.ID)
		if err != nil {
			logger.Error(err.Error())
			ctx.Error(err)
			return
		}
		if e == nil {
			ctx.Error(ErrCredentialNotFound)
			return
		}

		err = credentialRepo.Delete(e)
		if err != nil {
			logger.Error(err.Error())
			ctx.Error(err)
			return
		}
		ctx.OK(nil)
	})
