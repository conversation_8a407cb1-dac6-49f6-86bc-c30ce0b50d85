package smtp_credential_test

import (
	"net/http"

	"git.metadiv.io/metadiv-studio/metadev"
	"git.metadiv.io/metadiv-studio/metaorm"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"git.metadiv.io/metadiv-studio-modules/smtp_credential"
)

var _ = Describe("ApiSmtpCredentialCreate Handler", func() {
	var (
		mockDB     smtp_credential.MockDatabaseExecutor
		db         metaorm.Database
		engine     metadev.Engine
		apiHandler *metadev.ApiHandlerImpl[smtp_credential.CreateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO]
	)

	BeforeEach(func() {
		// Initialize test engine
		engine = metadev.NewTestEngine()

		// Get the API handler from the module
		apiHandler = smtp_credential.ApiSmtpCredentialCreate
	})

	AfterEach(func() {
		if mockDB != nil {
			mockDB.Close()
		}
	})

	setupDatabase := func() {
		mockDB = smtp_credential.NewMockDatabaseExecutor()
		db = mockDB.New()
	}

	Context("when creating a valid SMTP credential", func() {
		It("should create SMTP credential successfully", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.CreateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&smtp_credential.CreateSmtpCredentialRequest{
					Host:     "smtp.gmail.com",
					Port:     587,
					Username: "<EMAIL>",
					Password: "testpassword",
				},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &smtp_credential.CreateSmtpCredentialRequest{
				Host:     "smtp.gmail.com",
				Port:     587,
				Username: "<EMAIL>",
				Password: "testpassword",
			})

			// Verify response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(responseBody.Host).To(Equal("smtp.gmail.com"))
			Expect(responseBody.Port).To(Equal(587))
			Expect(responseBody.Username).To(Equal("<EMAIL>"))
			Expect(responseBody.Active).To(BeTrue())
		})

		It("should create SMTP credential with different port", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.CreateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&smtp_credential.CreateSmtpCredentialRequest{
					Host:     "smtp.outlook.com",
					Port:     465,
					Username: "<EMAIL>",
					Password: "outlookpass",
				},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &smtp_credential.CreateSmtpCredentialRequest{
				Host:     "smtp.outlook.com",
				Port:     465,
				Username: "<EMAIL>",
				Password: "outlookpass",
			})

			// Verify response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(responseBody.Host).To(Equal("smtp.outlook.com"))
			Expect(responseBody.Port).To(Equal(465))
			Expect(responseBody.Username).To(Equal("<EMAIL>"))
			Expect(responseBody.Active).To(BeTrue())
		})

		It("should create SMTP credential with custom domain", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.CreateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&smtp_credential.CreateSmtpCredentialRequest{
					Host:     "mail.example.com",
					Port:     25,
					Username: "<EMAIL>",
					Password: "custompass",
				},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &smtp_credential.CreateSmtpCredentialRequest{
				Host:     "mail.example.com",
				Port:     25,
				Username: "<EMAIL>",
				Password: "custompass",
			})

			// Verify response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(responseBody.Host).To(Equal("mail.example.com"))
			Expect(responseBody.Port).To(Equal(25))
			Expect(responseBody.Username).To(Equal("<EMAIL>"))
			Expect(responseBody.Active).To(BeTrue())
		})
	})

	Context("when validation fails", func() {
		It("should return error when host is missing", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.CreateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&smtp_credential.CreateSmtpCredentialRequest{
					Host:     "",
					Port:     587,
					Username: "<EMAIL>",
					Password: "testpassword",
				},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &smtp_credential.CreateSmtpCredentialRequest{
				Host:     "",
				Port:     587,
				Username: "<EMAIL>",
				Password: "testpassword",
			})

			// Verify error response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusBadRequest))
			Expect(ctx.GetResponse().Success).To(BeFalse())
			Expect(ctx.GetResponse().Error).To(Equal("host is required"))
		})

		It("should return error when port is missing", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.CreateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&smtp_credential.CreateSmtpCredentialRequest{
					Host:     "smtp.gmail.com",
					Port:     0,
					Username: "<EMAIL>",
					Password: "testpassword",
				},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &smtp_credential.CreateSmtpCredentialRequest{
				Host:     "smtp.gmail.com",
				Port:     0,
				Username: "<EMAIL>",
				Password: "testpassword",
			})

			// Verify error response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusBadRequest))
			Expect(ctx.GetResponse().Success).To(BeFalse())
			Expect(ctx.GetResponse().Error).To(Equal("port is required"))
		})

		It("should return error when username is missing", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.CreateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&smtp_credential.CreateSmtpCredentialRequest{
					Host:     "smtp.gmail.com",
					Port:     587,
					Username: "",
					Password: "testpassword",
				},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &smtp_credential.CreateSmtpCredentialRequest{
				Host:     "smtp.gmail.com",
				Port:     587,
				Username: "",
				Password: "testpassword",
			})

			// Verify error response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusBadRequest))
			Expect(ctx.GetResponse().Success).To(BeFalse())
			Expect(ctx.GetResponse().Error).To(Equal("username is required"))
		})

		It("should return error when password is missing", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.CreateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&smtp_credential.CreateSmtpCredentialRequest{
					Host:     "smtp.gmail.com",
					Port:     587,
					Username: "<EMAIL>",
					Password: "",
				},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &smtp_credential.CreateSmtpCredentialRequest{
				Host:     "smtp.gmail.com",
				Port:     587,
				Username: "<EMAIL>",
				Password: "",
			})

			// Verify error response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusBadRequest))
			Expect(ctx.GetResponse().Success).To(BeFalse())
			Expect(ctx.GetResponse().Error).To(Equal("password is required"))
		})
	})

	Context("when testing edge cases", func() {
		It("should handle special characters in fields", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.CreateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&smtp_credential.CreateSmtpCredentialRequest{
					Host:     "smtp.example-domain.com",
					Port:     587,
					Username: "<EMAIL>",
					Password: "p@ssw0rd!",
				},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &smtp_credential.CreateSmtpCredentialRequest{
				Host:     "smtp.example-domain.com",
				Port:     587,
				Username: "<EMAIL>",
				Password: "p@ssw0rd!",
			})

			// Verify response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(responseBody.Host).To(Equal("smtp.example-domain.com"))
			Expect(responseBody.Username).To(Equal("<EMAIL>"))
			Expect(responseBody.Password).To(Equal("p@ssw0rd!"))
		})

		It("should handle very long host names", func() {
			setupDatabase()

			longHost := "very-long-subdomain-name-that-exceeds-normal-length-limits.example.com"
			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.CreateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&smtp_credential.CreateSmtpCredentialRequest{
					Host:     longHost,
					Port:     587,
					Username: "<EMAIL>",
					Password: "testpassword",
				},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &smtp_credential.CreateSmtpCredentialRequest{
				Host:     longHost,
				Port:     587,
				Username: "<EMAIL>",
				Password: "testpassword",
			})

			// Verify response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(responseBody.Host).To(Equal(longHost))
		})

		It("should handle different port ranges", func() {
			setupDatabase()

			testCases := []int{25, 465, 587, 2525, 1025, 65535}

			for _, port := range testCases {
				ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.CreateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO](
					1,     // userId
					1,     // workspaceId
					1,     // workspaceUserId
					false, // isAdmin
					engine,
					apiHandler,
					&smtp_credential.CreateSmtpCredentialRequest{
						Host:     "smtp.example.com",
						Port:     port,
						Username: "<EMAIL>",
						Password: "testpassword",
					},
				)
				Expect(err).To(BeNil())

				// Execute the handler
				apiHandler.Handler(ctx, db, logger, &smtp_credential.CreateSmtpCredentialRequest{
					Host:     "smtp.example.com",
					Port:     port,
					Username: "<EMAIL>",
					Password: "testpassword",
				})

				// Verify response
				Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
				Expect(ctx.GetResponse().Success).To(BeTrue())
				responseBody := ctx.GetResponse().Data
				Expect(responseBody).ToNot(BeNil())
				Expect(responseBody.Port).To(Equal(port))
			}
		})
	})

	Context("when testing data persistence", func() {
		It("should persist data correctly in database", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.CreateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&smtp_credential.CreateSmtpCredentialRequest{
					Host:     "smtp.test.com",
					Port:     587,
					Username: "<EMAIL>",
					Password: "testpass",
				},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &smtp_credential.CreateSmtpCredentialRequest{
				Host:     "smtp.test.com",
				Port:     587,
				Username: "<EMAIL>",
				Password: "testpass",
			})

			// Verify response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())

			// Verify data is persisted by querying the repository
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)
			credentials, _, err := credentialRepo.FilteredPagedFindMany(
				&metaorm.PaginationQueryImpl{Page: 1, Size: 100},
				&metaorm.SortingQueryImpl{},
				"",
				false,
			)
			Expect(err).To(BeNil())
			Expect(credentials).ToNot(BeNil())

			// Find the created credential
			var foundCredential *smtp_credential.SMTPCredential
			for _, cred := range credentials {
				if cred.Host.Get() == "smtp.test.com" && cred.Username.Get() == "<EMAIL>" {
					foundCredential = &cred
					break
				}
			}
			Expect(foundCredential).ToNot(BeNil())
			Expect(foundCredential.Host.Get()).To(Equal("smtp.test.com"))
			Expect(foundCredential.Port.Get()).To(Equal(587))
			Expect(foundCredential.Username.Get()).To(Equal("<EMAIL>"))
			Expect(foundCredential.Active.Get()).To(BeTrue())
		})

		It("should set active to true by default", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.CreateSmtpCredentialRequest, smtp_credential.SMTPCredentialDTO](
				1,     // userId
				1,     // workspaceId
				1,     // workspaceUserId
				false, // isAdmin
				engine,
				apiHandler,
				&smtp_credential.CreateSmtpCredentialRequest{
					Host:     "smtp.default.com",
					Port:     587,
					Username: "<EMAIL>",
					Password: "defaultpass",
				},
			)
			Expect(err).To(BeNil())

			// Execute the handler
			apiHandler.Handler(ctx, db, logger, &smtp_credential.CreateSmtpCredentialRequest{
				Host:     "smtp.default.com",
				Port:     587,
				Username: "<EMAIL>",
				Password: "defaultpass",
			})

			// Verify response
			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(responseBody.Active).To(BeTrue())
		})
	})
})
