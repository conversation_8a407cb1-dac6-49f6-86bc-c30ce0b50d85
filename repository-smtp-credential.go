package smtp_credential

import "git.metadiv.io/metadiv-studio/metaorm"

func NewSMTPCredentialRepository(db metaorm.Database) SMTPCredentialRepository {
	return &SMTPCredentialRepositoryImpl{
		RepositoryImpl: metaorm.NewRepositoryImpl[SMTPCredential](db),
	}
}

type SMTPCredentialRepository interface {
	metaorm.Repository[SMTPCredential]
	FindById(id uint) (*SMTPCredential, error)
	FilteredPagedFindMany(
		page *metaorm.PaginationQueryImpl,
		sort *metaorm.SortingQueryImpl,
		keyword string,
		activeOnly bool,
	) ([]SMTPCredential, *metaorm.Pagination, error)
}

type SMTPCredentialRepositoryImpl struct {
	metaorm.RepositoryImpl[SMTPCredential]
}

func (r *SMTPCredentialRepositoryImpl) FindById(id uint) (*SMTPCredential, error) {
	qb := metaorm.NewQueryBuilder()
	return r.Query(qb.Field("id").Eq(id)).FindOne()
}

func (r *SMTPCredentialRepositoryImpl) FilteredPagedFindMany(
	page *metaorm.PaginationQueryImpl,
	sort *metaorm.SortingQueryImpl,
	keyword string,
	activeOnly bool,
) ([]SMTPCredential, *metaorm.Pagination, error) {
	qb := metaorm.NewQueryBuilder()
	queries := make([]metaorm.Query, 0)
	if activeOnly {
		queries = append(queries, qb.Field("active").Eq(true))
	}
	if keyword != "" {
		queries = append(queries, qb.Or(
			qb.Field("host").Similar(keyword),
			qb.Field("username").Similar(keyword),
			qb.Field("port").Similar(keyword),
		))
	}
	return r.Query(qb.And(queries...)).
		Pagination(page).
		Sorting(sort).
		PagedFindMany()
}
