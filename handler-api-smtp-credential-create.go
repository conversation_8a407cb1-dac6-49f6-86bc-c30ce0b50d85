package smtp_credential

import (
	"git.metadiv.io/metadiv-studio/metadev"
	"git.metadiv.io/metadiv-studio/metaorm"
)

type CreateSmtpCredentialRequest struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
}

func (r *CreateSmtpCredentialRequest) Validate() error {
	if r.Host == "" {
		return ErrHostRequired
	}
	if r.Port == 0 {
		return ErrPortRequired
	}
	if r.Username == "" {
		return ErrUsernameRequired
	}
	if r.Password == "" {
		return ErrPasswordRequired
	}
	return nil
}

func (r *CreateSmtpCredentialRequest) ToEntity() *SMTPCredential {
	e := &SMTPCredential{}
	e.Host.Set(r.Host)
	e.Port.Set(r.Port)
	e.Username.Set(r.Username)
	e.Password.Set(r.Password)
	e.Active.Set(true)
	return e
}

var ApiSmtpCredentialCreate = metadev.Get[CreateSmtpCredentialRequest, SMTPCredentialDTO](Module).
	Name("createSmtpCredential").
	Route("/smtp/credential").
	Description(`
Create a new SMTP credential.
`).
	Handler(func(ctx metadev.ApiContext[CreateSmtpCredentialRequest, SMTPCredentialDTO], db metaorm.Database, logger metadev.Logger, req *CreateSmtpCredentialRequest) {

		e := req.ToEntity()

		credentialRepo := NewSMTPCredentialRepository(db)
		e, err := credentialRepo.Save(e).Model()
		if err != nil {
			logger.Error(err.Error())
			ctx.Error(err)
			return
		}

		ctx.OK(e.DTO())
	})
