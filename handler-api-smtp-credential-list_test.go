package smtp_credential_test

import (
	"net/http"

	"git.metadiv.io/metadiv-studio/metadev"
	"git.metadiv.io/metadiv-studio/metaorm"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"git.metadiv.io/metadiv-studio-modules/smtp_credential"
)

var _ = Describe("ApiSmtpCredentialList Handler", func() {
	var (
		mockDB     smtp_credential.MockDatabaseExecutor
		db         metaorm.Database
		engine     metadev.Engine
		apiHandler *metadev.ApiHandlerImpl[smtp_credential.ListSmtpCredentialRequest, []smtp_credential.SMTPCredentialDTO]
	)

	BeforeEach(func() {
		engine = metadev.NewTestEngine()
		apiHandler = smtp_credential.ApiSmtpCredentialList
	})

	AfterEach(func() {
		if mockDB != nil {
			mockDB.Close()
		}
	})

	setupDatabase := func() {
		mockDB = smtp_credential.NewMockDatabaseExecutor()
		db = mockDB.New()
	}

	createTestCredential := func(repo smtp_credential.SMTPCredentialRepository, host, username string, port int, active bool) *smtp_credential.SMTPCredential {
		credential := &smtp_credential.SMTPCredential{}
		credential.Host.Set(host)
		credential.Port.Set(port)
		credential.Username.Set(username)
		credential.Password.Set("testpassword")
		credential.Active.Set(active)
		savedCredential, err := repo.Save(credential).Model()
		Expect(err).To(BeNil())
		return savedCredential
	}

	Context("when listing SMTP credentials", func() {
		It("should return all credentials successfully", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)

			// Create test credentials
			createTestCredential(credentialRepo, "smtp.gmail.com", "<EMAIL>", 587, true)
			createTestCredential(credentialRepo, "smtp.outlook.com", "<EMAIL>", 465, true)
			createTestCredential(credentialRepo, "mail.example.com", "<EMAIL>", 25, false)

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.ListSmtpCredentialRequest, []smtp_credential.SMTPCredentialDTO](
				1, 1, 1, false, engine, apiHandler,
				&smtp_credential.ListSmtpCredentialRequest{
					RequestListing: metadev.RequestListing{
						PaginationQueryImpl: metaorm.PaginationQueryImpl{Page: 1, Size: 10},
						SortingQueryImpl:    metaorm.SortingQueryImpl{},
						Keyword:             "",
					},
					ActiveOnly: false,
				},
			)
			Expect(err).To(BeNil())

			apiHandler.Handler(ctx, db, logger, &smtp_credential.ListSmtpCredentialRequest{
				RequestListing: metadev.RequestListing{
					PaginationQueryImpl: metaorm.PaginationQueryImpl{Page: 1, Size: 10},
					SortingQueryImpl:    metaorm.SortingQueryImpl{},
					Keyword:             "",
				},
				ActiveOnly: false,
			})

			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(len(*responseBody)).To(Equal(3))
		})

		It("should return only active credentials when ActiveOnly is true", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)

			// Create test credentials - 2 active, 1 inactive
			createTestCredential(credentialRepo, "smtp.gmail.com", "<EMAIL>", 587, true)
			createTestCredential(credentialRepo, "smtp.outlook.com", "<EMAIL>", 465, true)
			createTestCredential(credentialRepo, "mail.example.com", "<EMAIL>", 25, false)

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.ListSmtpCredentialRequest, []smtp_credential.SMTPCredentialDTO](
				1, 1, 1, false, engine, apiHandler,
				&smtp_credential.ListSmtpCredentialRequest{
					RequestListing: metadev.RequestListing{
						PaginationQueryImpl: metaorm.PaginationQueryImpl{Page: 1, Size: 10},
						SortingQueryImpl:    metaorm.SortingQueryImpl{},
						Keyword:             "",
					},
					ActiveOnly: true,
				},
			)
			Expect(err).To(BeNil())

			apiHandler.Handler(ctx, db, logger, &smtp_credential.ListSmtpCredentialRequest{
				RequestListing: metadev.RequestListing{
					PaginationQueryImpl: metaorm.PaginationQueryImpl{Page: 1, Size: 10},
					SortingQueryImpl:    metaorm.SortingQueryImpl{},
					Keyword:             "",
				},
				ActiveOnly: true,
			})

			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(len(*responseBody)).To(Equal(2))

			// Verify all returned credentials are active
			for _, cred := range *responseBody {
				Expect(cred.Active).To(BeTrue())
			}
		})

		It("should filter by keyword", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)

			createTestCredential(credentialRepo, "smtp.gmail.com", "<EMAIL>", 587, true)
			createTestCredential(credentialRepo, "smtp.outlook.com", "<EMAIL>", 465, true)
			createTestCredential(credentialRepo, "mail.example.com", "<EMAIL>", 25, true)

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.ListSmtpCredentialRequest, []smtp_credential.SMTPCredentialDTO](
				1, 1, 1, false, engine, apiHandler,
				&smtp_credential.ListSmtpCredentialRequest{
					RequestListing: metadev.RequestListing{
						PaginationQueryImpl: metaorm.PaginationQueryImpl{Page: 1, Size: 10},
						SortingQueryImpl:    metaorm.SortingQueryImpl{},
						Keyword:             "gmail",
					},
					ActiveOnly: false,
				},
			)
			Expect(err).To(BeNil())

			apiHandler.Handler(ctx, db, logger, &smtp_credential.ListSmtpCredentialRequest{
				RequestListing: metadev.RequestListing{
					PaginationQueryImpl: metaorm.PaginationQueryImpl{Page: 1, Size: 10},
					SortingQueryImpl:    metaorm.SortingQueryImpl{},
					Keyword:             "gmail",
				},
				ActiveOnly: false,
			})

			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(len(*responseBody)).To(BeNumerically(">=", 1))
		})
	})

	Context("when testing pagination", func() {
		It("should handle pagination correctly", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)

			// Create multiple credentials
			for i := 1; i <= 5; i++ {
				createTestCredential(credentialRepo, "smtp.example.com", "user"+string(rune(i))+"@example.com", 587, true)
			}

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.ListSmtpCredentialRequest, []smtp_credential.SMTPCredentialDTO](
				1, 1, 1, false, engine, apiHandler,
				&smtp_credential.ListSmtpCredentialRequest{
					RequestListing: metadev.RequestListing{
						PaginationQueryImpl: metaorm.PaginationQueryImpl{Page: 1, Size: 3},
						SortingQueryImpl:    metaorm.SortingQueryImpl{},
						Keyword:             "",
					},
					ActiveOnly: false,
				},
			)
			Expect(err).To(BeNil())

			apiHandler.Handler(ctx, db, logger, &smtp_credential.ListSmtpCredentialRequest{
				RequestListing: metadev.RequestListing{
					PaginationQueryImpl: metaorm.PaginationQueryImpl{Page: 1, Size: 3},
					SortingQueryImpl:    metaorm.SortingQueryImpl{},
					Keyword:             "",
				},
				ActiveOnly: false,
			})

			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(len(*responseBody)).To(BeNumerically("<=", 3))
		})
	})

	Context("when no credentials exist", func() {
		It("should return empty list", func() {
			setupDatabase()

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.ListSmtpCredentialRequest, []smtp_credential.SMTPCredentialDTO](
				1, 1, 1, false, engine, apiHandler,
				&smtp_credential.ListSmtpCredentialRequest{
					RequestListing: metadev.RequestListing{
						PaginationQueryImpl: metaorm.PaginationQueryImpl{Page: 1, Size: 10},
						SortingQueryImpl:    metaorm.SortingQueryImpl{},
						Keyword:             "",
					},
					ActiveOnly: false,
				},
			)
			Expect(err).To(BeNil())

			apiHandler.Handler(ctx, db, logger, &smtp_credential.ListSmtpCredentialRequest{
				RequestListing: metadev.RequestListing{
					PaginationQueryImpl: metaorm.PaginationQueryImpl{Page: 1, Size: 10},
					SortingQueryImpl:    metaorm.SortingQueryImpl{},
					Keyword:             "",
				},
				ActiveOnly: false,
			})

			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(len(*responseBody)).To(Equal(0))
		})
	})

	Context("when testing edge cases", func() {
		It("should handle large page size", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)

			createTestCredential(credentialRepo, "smtp.test.com", "<EMAIL>", 587, true)

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.ListSmtpCredentialRequest, []smtp_credential.SMTPCredentialDTO](
				1, 1, 1, false, engine, apiHandler,
				&smtp_credential.ListSmtpCredentialRequest{
					RequestListing: metadev.RequestListing{
						PaginationQueryImpl: metaorm.PaginationQueryImpl{Page: 1, Size: 1000},
						SortingQueryImpl:    metaorm.SortingQueryImpl{},
						Keyword:             "",
					},
					ActiveOnly: false,
				},
			)
			Expect(err).To(BeNil())

			apiHandler.Handler(ctx, db, logger, &smtp_credential.ListSmtpCredentialRequest{
				RequestListing: metadev.RequestListing{
					PaginationQueryImpl: metaorm.PaginationQueryImpl{Page: 1, Size: 1000},
					SortingQueryImpl:    metaorm.SortingQueryImpl{},
					Keyword:             "",
				},
				ActiveOnly: false,
			})

			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
		})

		It("should handle empty keyword search", func() {
			setupDatabase()
			credentialRepo := smtp_credential.NewSMTPCredentialRepository(db)

			createTestCredential(credentialRepo, "smtp.test.com", "<EMAIL>", 587, true)

			ctx, logger, err := metadev.NewApiTestContextForWorkspaceUser[smtp_credential.ListSmtpCredentialRequest, []smtp_credential.SMTPCredentialDTO](
				1, 1, 1, false, engine, apiHandler,
				&smtp_credential.ListSmtpCredentialRequest{
					RequestListing: metadev.RequestListing{
						PaginationQueryImpl: metaorm.PaginationQueryImpl{Page: 1, Size: 10},
						SortingQueryImpl:    metaorm.SortingQueryImpl{},
						Keyword:             "",
					},
					ActiveOnly: false,
				},
			)
			Expect(err).To(BeNil())

			apiHandler.Handler(ctx, db, logger, &smtp_credential.ListSmtpCredentialRequest{
				RequestListing: metadev.RequestListing{
					PaginationQueryImpl: metaorm.PaginationQueryImpl{Page: 1, Size: 10},
					SortingQueryImpl:    metaorm.SortingQueryImpl{},
					Keyword:             "",
				},
				ActiveOnly: false,
			})

			Expect(ctx.GetHttpStatus()).To(Equal(http.StatusOK))
			Expect(ctx.GetResponse().Success).To(BeTrue())
			responseBody := ctx.GetResponse().Data
			Expect(responseBody).ToNot(BeNil())
			Expect(len(*responseBody)).To(Equal(1))
		})
	})
})
